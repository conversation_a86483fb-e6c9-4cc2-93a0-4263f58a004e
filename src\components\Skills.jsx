import React, { useState } from "react";
import { ScrollObserver } from "./ScrollObserver";
import {
  SiPython,
  SiMysql,
  SiFlask,
  SiFastapi,
  SiHtml5,
  SiCss3,
  SiTailwindcss,
  SiReact
} from "react-icons/si";
import { FaJava } from "react-icons/fa";

const skillCategories = [
  {
    title: "Programming Languages",
    color: "cyan",
    skills: [
      {
        name: "Python",
        icon: <SiPython className="w-8 h-8" />,
        color: "yellow",
        level: 90,
      },
      {
        name: "Java",
        icon: <FaJava className="w-8 h-8" />,
        color: "red",
        level: 80,
      },
    ]
  },
  {
    title: "Web Designing",
    color: "purple",
    skills: [
      {
        name: "HTML",
        icon: <SiHtml5 className="w-8 h-8" />,
        color: "orange",
        level: 95,
      },
      {
        name: "CSS",
        icon: <SiCss3 className="w-8 h-8" />,
        color: "cyan",
        level: 90,
      },
      {
        name: "Tailwind CSS",
        icon: <SiTailwindcss className="w-8 h-8" />,
        color: "cyan",
        level: 85,
      },
      {
        name: "React",
        icon: <SiReact className="w-8 h-8" />,
        color: "cyan",
        level: 85,
      },
    ]
  },
  {
    title: "Backend Frameworks",
    color: "green",
    skills: [
      {
        name: "Flask",
        icon: <SiFlask className="w-8 h-8" />,
        color: "green",
        level: 80,
      },
      {
        name: "FastAPI",
        icon: <SiFastapi className="w-8 h-8" />,
        color: "green",
        level: 75,
      },
    ]
  },
  {
    title: "Databases",
    color: "pink",
    skills: [
      {
        name: "MySQL",
        icon: <SiMysql className="w-8 h-8" />,
        color: "pink",
        level: 85,
      },
    ]
  },
];
const SkillCard = ({ skill, index, categoryIndex }) => {
  const [isHovered, setIsHovered] = useState(false);

  return (
    <ScrollObserver
      animation="slide-up"
      delay={(categoryIndex * 0.2) + (index * 0.1)}
      key={skill.name}
      threshold={0.2}
    >
      <div
        className="skill-card group relative"
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
        style={{
          color: `var(--neon-${skill.color})`,
        }}
      >
        <div className="skill-card-inner">
          <div className="skill-icon-container">
            {skill.icon}
          </div>
          <h4 className="skill-name">{skill.name}</h4>
          <div className="skill-progress-container">
            <div
              className="skill-progress-bar"
              style={{
                width: isHovered ? `${skill.level}%` : '0%',
                backgroundColor: `var(--neon-${skill.color})`,
                boxShadow: `0 0 10px var(--neon-${skill.color})`,
              }}
            />
            <span className="skill-percentage">{skill.level}%</span>
          </div>
        </div>
        <div
          className="skill-card-glow"
          style={{
            background: `radial-gradient(circle, var(--neon-${skill.color})20 0%, transparent 70%)`,
          }}
        />
      </div>
    </ScrollObserver>
  );
};

const SkillCategory = ({ category, categoryIndex }) => {
  const [isExpanded, setIsExpanded] = useState(true);

  return (
    <ScrollObserver animation="fade-in" delay={categoryIndex * 0.3} threshold={0.1}>
      <div className="skill-category">
        <div
          className="skill-category-header"
          onClick={() => setIsExpanded(!isExpanded)}
          style={{ color: `var(--neon-${category.color})` }}
        >
          <div className="skill-category-title">
            <span className="skill-category-icon">{category.icon}</span>
            <h3 className="skill-category-name">{category.title}</h3>
          </div>
          <div className={`skill-category-toggle ${isExpanded ? 'expanded' : ''}`}>
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
            </svg>
          </div>
        </div>

        <div className={`skill-category-content ${isExpanded ? 'expanded' : 'collapsed'}`}>
          <div className="skills-grid">
            {category.skills.map((skill, index) => (
              <SkillCard
                key={skill.name}
                skill={skill}
                index={index}
                categoryIndex={categoryIndex}
              />
            ))}
          </div>
        </div>
      </div>
    </ScrollObserver>
  );
};

const Skills = () => {
  return (
    <section id="skills" className="py-20 relative overflow-hidden bg-gradient-to-b from-background to-background/70">
      <div className="absolute inset-0 overflow-hidden opacity-20">
        <div className="absolute top-0 left-0 w-full h-full grid-pattern" />
        <div className="floating-particles">
          {[...Array(20)].map((_, i) => (
            <div
              key={i}
              className="particle"
              style={{
                left: `${Math.random() * 100}%`,
                animationDelay: `${Math.random() * 10}s`,
                animationDuration: `${10 + Math.random() * 20}s`,
              }}
            />
          ))}
        </div>
      </div>

      <div className="container mx-auto px-6 relative z-10">
        <ScrollObserver animation="slide-up">
          <div className="text-center mb-16">
            <h2 className="section-heading text-neon-cyan">My Skills</h2>
            <p className="text-muted-foreground text-lg mt-4 max-w-2xl mx-auto">
              Explore my technical expertise across different domains of software development
            </p>
          </div>
        </ScrollObserver>

        <div className="skills-container">
          {skillCategories.map((category, index) => (
            <SkillCategory
              key={category.title}
              category={category}
              categoryIndex={index}
            />
          ))}
        </div>
      </div>
    </section>
  );
};
export default Skills;
