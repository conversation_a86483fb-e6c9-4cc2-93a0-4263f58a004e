import React, { useState, useEffect } from "react";
import { Menu, X } from "lucide-react";
const navLinks = [
    { name: "Home", href: "#home" },
    { name: "About", href: "#about" },
    { name: "Education", href: "#education" }, // Add Education link
    { name: "Skills", href: "#skills" },
    { name: "Projects", href: "#projects" },
    { name: "Contact", href: "#contact" },
];
const Navbar = () => {
    const [isScrolled, setIsScrolled] = useState(false);
    const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
    const [activeSection, setActiveSection] = useState("home");
    useEffect(() => {
        const handleScroll = () => {
            if (window.scrollY > 50) {
                setIsScrolled(true);
            }
            else {
                setIsScrolled(false);
            }
            // Track active section
            const sections = document.querySelectorAll("section[id]");
            sections.forEach((section) => {
                // Use HTMLElement instead of Element to access offsetTop property
                const sectionElement = section;
                const sectionTop = sectionElement.offsetTop;
                const sectionHeight = sectionElement.clientHeight;
                if (window.scrollY >= sectionTop - 200 && window.scrollY < sectionTop + sectionHeight - 200) {
                    setActiveSection(section.getAttribute("id") || "");
                }
            });
        };
        window.addEventListener("scroll", handleScroll);
        return () => window.removeEventListener("scroll", handleScroll);
    }, []);
    return (<nav className={`fixed top-0 left-0 w-full z-50 transition-all duration-300 ${isScrolled ? "bg-background/90 backdrop-blur-md py-2 shadow-md" : "py-4"}`}>
      <div className="container mx-auto px-6 flex items-center justify-between">
        <a href="#home" className="neon-text-cyan font-orbitron text-xl font-bold">
          Harshith
        </a>

        {/* Desktop Menu */}
        <div className="hidden md:flex items-center space-x-4">
          {navLinks.map((link) => (<a key={link.name} href={link.href} className={`nav-item ${activeSection === link.href.substring(1) ? "nav-item-active neon-text-cyan" : ""}`}>
              {link.name}
            </a>))}
        </div>

        {/* Mobile Menu Button */}
        <button className="md:hidden text-foreground" onClick={() => setMobileMenuOpen(!mobileMenuOpen)}>
          {mobileMenuOpen ? (<X className="h-6 w-6"/>) : (<Menu className="h-6 w-6"/>)}
        </button>
      </div>

      {/* Mobile Menu */}
      <div className={`fixed inset-0 z-40 bg-background/95 backdrop-blur-lg transform transition-transform duration-300 ease-in-out md:hidden ${mobileMenuOpen ? "translate-x-0" : "-translate-x-full"}`}>
        <div className="flex flex-col h-full justify-center items-center space-y-8 p-8">
          {navLinks.map((link) => (<a key={link.name} href={link.href} className={`text-2xl font-orbitron ${activeSection === link.href.substring(1) ? "neon-text-cyan" : ""}`} onClick={() => setMobileMenuOpen(false)}>
              {link.name}
            </a>))}
        </div>
      </div>
    </nav>);
};
export default Navbar;
