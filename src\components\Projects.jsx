import React, { useState } from "react";
import { ScrollObserver } from "./ScrollObserver";
import { ExternalLink, Github } from "lucide-react";
const projects = [
    {
        title: "Attendance Tracker",
        description: "A web application that allows users to track attendance of students in a class and also calculate the no of classes they can skip per week",
        tags: ["Python", "HTML", "CSS", "JavaScript", "Selenium"],
        image: "https://images.unsplash.com/photo-1434030216411-0b793f4b4173?w=800&q=80", // Students in classroom/education
        demoLink: "https://attendly-039.onrender.com/",
        codeLink: "https://github.com/Harshith106/Attendly",
        color: "cyan",
        category: "web",
    },
    {
        title: "Personal Portfolio",
        description: "A personal portfolio website to showcase my projects and skills",
        tags: ["React", "TypeScript", "Tailwind", "Vercel"],
        image: "https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=800&q=80", // Developer workspace/coding
        demoLink: "https://harshith-dev.netlify.app/",
        codeLink: "https://github.com/Harshith106/Portfolio",
        color: "green",
        category: "web",
    },
    {
        title: "Weather Forecast website",
        description: "A website that shows the weather of a city including the temperature, humidity, wind speed, and other weather details for hourly basis, 3 day forecast and 7 day forecast",
        tags: ["React", "Tailwind", "OpenWeatherMap API"],
        image: "https://images.unsplash.com/photo-1504608524841-42fe6f032b4b?w=800&q=80", // Weather/storm clouds
        demoLink: "https://github.com/Harshith106/ReactWeather",
        codeLink: "https://github.com/Harshith106/ReactWeather",
        color: "pink",
        category: "mobile",
    },
    {
        title: "Voxtora",
        description: "An ai tool that will take a youtube video link and generate the structured notes based on the content delivered by the lecture.",
        tags: ["Python", "Fast Api", "React js"],
        image: "https://images.unsplash.com/photo-1677442136019-21780ecad995?w=800&q=80", // AI/Machine Learning concept
        demoLink: "https://github.com/Harshith106/VoxtoraNotesGenerator",
        codeLink: "https://github.com/Harshith106/VoxtoraNotesGenerator",
        color: "purple",
        category: "ml",
    }
];
const Projects = () => {
    const [activeFilter, setActiveFilter] = useState("all");
    const filteredProjects = activeFilter === "all"
        ? projects
        : projects.filter(project => project.category === activeFilter);
    return (<section id="projects" className="py-20 relative overflow-hidden">
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-0 left-0 w-full h-full grid-pattern opacity-30"/>
      </div>
      
      <div className="container mx-auto px-6 relative z-10">
        <ScrollObserver animation="rotate-in">
          <h2 className="section-heading neon-text-pink text-center">My Projects</h2>
        </ScrollObserver>
        
        {/* Auto-fit grid for project cards */}
        <div className="grid gap-8" style={{ gridTemplateColumns: 'repeat(auto-fit, minmax(500px, 1fr))' }}>
          {filteredProjects.slice(0, 4).map((project, index) => (<ScrollObserver key={project.title} animation="zoom-in" delay={index * 0.1} threshold={0.2}>
              <div className={`project-card text-neon-${project.color}`}>
                <div className="relative h-48 overflow-hidden">
                  <div className="absolute inset-0 bg-gradient-to-t from-background to-transparent z-10"/>
                  <img src={project.image} alt={project.title} className="w-full h-full object-cover transition-transform duration-500 hover:scale-110"/>
                </div>
                <div className="p-6 min-h-[280px] flex flex-col">
                  <h3 className="text-xl font-orbitron font-bold mb-2">{project.title}</h3>
                  <p className="text-foreground/80 mb-4 flex-grow">{project.description}</p>
                  <div className="flex flex-wrap gap-2 mb-4">
                    {project.tags.map(tag => (<span key={tag} className={`px-2 py-1 text-xs rounded-full bg-neon-${project.color}/10 border border-neon-${project.color}/30`}>
                        {tag}
                      </span>))}
                  </div>
                  <div className="flex gap-3 mt-auto">
                    <a href={project.demoLink} className="flex items-center gap-1 hover:underline" target="_blank" rel="noopener noreferrer">
                      <ExternalLink size={16}/>
                      Demo
                    </a>
                    <a href={project.codeLink} className="flex items-center gap-1 hover:underline" target="_blank" rel="noopener noreferrer">
                      <Github size={16}/>
                      Code
                    </a>
                  </div>
                </div>
              </div>
            </ScrollObserver>))}
        </div>

        {/* Full-width currently working project card */}
        <div className="mt-8">
          <div className="project-card bg-gradient-to-r from-neon-cyan/10 via-neon-green/10 to-neon-purple/10 border-2 border-neon-green p-8 rounded-2xl flex flex-col items-center text-center shadow-lg">
            <div className="text-neon-green text-lg font-bold mb-2 animate-pulse">Currently working...</div>
            <h3 className="text-2xl font-orbitron font-bold mb-2 neon-text-cyan">AI Language Learning App</h3>
            <p className="text-lg text-foreground/90 max-w-2xl mx-auto">An app that uses AI to help users learn new languages effectively. It uses a combination of NLP and machine learning to provide personalized learning experiences. Where Ai tracks user mistakes and tailors the lesson plan accordingly</p>
          </div>
        </div>
      </div>
    </section>);
};
export default Projects;
